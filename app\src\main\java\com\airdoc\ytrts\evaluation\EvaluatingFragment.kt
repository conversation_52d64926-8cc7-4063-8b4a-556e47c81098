package com.airdoc.ytrts.evaluation

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import com.airdoc.component.common.base.BaseBindingFragment
import com.airdoc.component.common.ktx.countdown
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.TimeUtils
import com.airdoc.ytrts.R
import com.airdoc.component.common.R as CommonR
import com.airdoc.ytrts.databinding.FragmentEvaluatingBinding
import com.airdoc.ytrts.home.vm.EvaluationViewModel
import com.airdoc.ytrts.ppg.PPGManager
import com.airdoc.ytrts.ppg.bean.PPGDataPoint
import com.airdoc.ytrts.ppg.vm.PpgViewModel
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lepu.blepro.constants.Ble
import com.lepu.blepro.event.InterfaceEvent
import com.lepu.blepro.ext.pc60fw.RtParam
import com.lepu.blepro.ext.pc60fw.RtWave
import com.lepu.blepro.ext.pc60fw.WorkingStatus
import com.lepu.blepro.objs.Bluetooth
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import kotlin.getValue
import androidx.core.graphics.toColorInt
import com.airdoc.ytrts.ppg.bean.AnalysisResult

/**
 * FileName: EvaluatingFragment
 * Author by lilin,Date on 2025/6/18 11:32
 * PS: Not easy to write code, please indicate.
 * 评估中
 */
class EvaluatingFragment : BaseBindingFragment<FragmentEvaluatingBinding>(){

    companion object{
        private val TAG = EvaluatingFragment::class.java.simpleName

        const val FRAGMENT_TAG = "EVALUATING"
        private const val INPUT_PATIENT_ID = "PATIENT_ID"

        fun newInstance(patientId: Long): EvaluatingFragment {
            val fragment = EvaluatingFragment()
            val bundle = Bundle()
            bundle.putLong(INPUT_PATIENT_ID, patientId)
            fragment.arguments = bundle
            return fragment
        }
    }

    private val ppgVM by activityViewModels<PpgViewModel>()
    private val evaluationVM by activityViewModels<EvaluationViewModel>()
    private val gson = Gson()
    //保存数据点
    private val ppgDataPointList = mutableListOf<PPGDataPoint>()
    private var mPatientId = 0L
    //当次采集数据保存的文件名
    private var simpleDateFormat = ""
    //采集数据保存的文件
    private var ppgDataFile:File? = null
    //数据采集是否完成
    private var isComplete = false
    //数据采集是否终止
    private var isTerminate = false
    //倒计时任务
    private var timeCountdownJob:Job? = null
    //数据分析结果
    private var ppgAnalysisResult: AnalysisResult? = null
    //蓝牙状态
    private var bleState = Ble.State.DISCONNECTED

    override fun createBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentEvaluatingBinding {
        return FragmentEvaluatingBinding.inflate(inflater,container,false)
    }

    override fun initParam() {
        simpleDateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).apply {
            timeZone = TimeZone.getDefault() // 显式设置设备时区
        }.format(Date())
        ppgDataFile = PPGManager.createTimestampedFolder(mActivity,simpleDateFormat)
        mPatientId = arguments?.getLong(INPUT_PATIENT_ID) ?: 0
    }

    override fun initView() {
        initListener()
        startTimeCountdown()
    }

    override fun initObserver() {
        ppgVM.ppgDeviceLiveData.observe(this) {
            updatePpgDevice(it)
        }
        ppgVM.bleStateLiveData.observe(this) {
            updateBleState(it)
        }
        evaluationVM.reportEvaluationLiveData.observe(this) {
            if (!it?.reportId.isNullOrEmpty()){
                Toast.makeText(mActivity, getString(R.string.str_submission_successful), Toast.LENGTH_SHORT).show()
                mActivity.finish()
            }else{
                Toast.makeText(mActivity, getString(R.string.str_submission_failed), Toast.LENGTH_SHORT).show()
            }
        }
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam).observe(this) {
            val data = it.data
            Logger.d(TAG, msg = "EventPC60FwRtParam data = $data")
            if (data is RtParam){
                binding.tvSpo2.text = getString(R.string.str_spo2_d,data.spo2)
                binding.tvPr.text = getString(R.string.str_pr_d,data.pr)
                binding.tvPi.text = getString(R.string.str_pi_f,data.pi)
            }
        }
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave).observe(this) {
            val data = it.data
            if (data is RtWave){
                Logger.d(TAG, msg = "EventPC60FwRtWave waveIntData = ${gson.toJson(data.waveIntData)}")
                if (!isComplete && !isTerminate){
                    val ints = data.waveIntData.toList()
                    ints.forEachIndexed { index, value ->
                        if (ppgDataPointList.isEmpty()){
                            ppgDataPointList.add(PPGDataPoint(value.toDouble(), System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L))
                        }else{
                            //间隔20毫秒，转成纳秒
                            ppgDataPointList.add(PPGDataPoint(value.toDouble(),ppgDataPointList[ppgDataPointList.size - 1].timestamp + 20 * 1_000_000L))
                        }

                    }
                    binding.ecgView.addDataPoints(ints)
                }
            }
        }
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwBatLevel).observe(this) {
            val data = it.data
            Logger.d(TAG, msg = "EventPC60FwBatLevel data = $data")
        }
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwWorkingStatus).observe(this) {
            val data = it.data
            if (data is WorkingStatus){
                Logger.d(TAG, msg = "EventPC60FwWorkingStatus data = $data")
            }
        }
    }

    override fun initData() {
        updatePpgDevice(ppgVM.getPpgDevice())
        updateBleState(ppgVM.getBleState())
    }

    private fun initListener(){
        binding.tvCancel.setOnSingleClickListener {
            mActivity.finish()
        }
        binding.tvCommit.setOnSingleClickListener {
            reportEvaluationData()
        }
        binding.tvTerminate.setOnSingleClickListener {
            terminateEvaluation()
        }
        binding.tvRetake.setOnSingleClickListener {
            retakeEvaluation()
        }
    }

    private fun updatePpgDevice(bluetooth:Bluetooth?){
        Logger.d(TAG, msg = "updatePpgDevice bluetooth = $${bluetooth?.name?:""} ${bluetooth?.macAddr?:""}")
        binding.tvDeviceName.text = bluetooth?.name.orEmpty()
    }

    private fun updateBleState(state:Int?){
        when(state){
            Ble.State.CONNECTED -> {
                bleState = Ble.State.CONNECTED
                binding.ivBleState.setImageResource(R.drawable.ic_bluetooth_connected)
                binding.tvBleState.text = getString(R.string.str_connected)
                binding.tvBleState.setTextColor(ContextCompat.getColor(requireContext(),R.color.color_1296db))
            }
            else -> {
                if (!isComplete && bleState == Ble.State.CONNECTED){
                    evaluatingAnomaly()
                }
                bleState = Ble.State.DISCONNECTED
                binding.ivBleState.setImageResource(R.drawable.ic_bluetooth_not_connected)
                binding.tvBleState.text = getString(R.string.str_disconnected)
                binding.tvBleState.setTextColor(ContextCompat.getColor(requireContext(), CommonR.color.color_666666))

            }
        }
    }

    /**
     * 开始计时
     */
    private fun startTimeCountdown(){
        stopTimeCountdown()
        timeCountdownJob = countdown(300 * 1000,1000,
            onTick = {
                if (it == 0L){
                    binding.tvTime.text = "00:00"
                }else{
                    binding.tvTime.text = TimeUtils.parseTimeToTimeString(it,"mm:ss")
                }
            },
            onCompletion = { th ->
                if (th == null){
                    completeEvaluation()
                }
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCatch ${th.message}")
            }
        )
    }

    private fun stopTimeCountdown(){
        timeCountdownJob?.cancel()
        timeCountdownJob = null
    }

    /**
     * 评估完成
     */
    private fun completeEvaluation(){
        isComplete = true
        isTerminate = false
        binding.tvTerminate.isVisible = false
        binding.ecgView.setDisplayMode(ECGView.DisplayMode.STATIC)
        ppgAnalysisResult = PPGManager.analyzeECG(ppgDataPointList)?.apply {
            patientId = mPatientId
        }
        binding.tvCollectionState.isVisible = true
        if (ppgAnalysisResult != null){
            binding.tvCollectionState.text = getString(R.string.str_data_collection_succeeded)
            binding.tvCollectionState.setTextColor("#00FF00".toColorInt())
            binding.tvCancel.isVisible = true
            binding.tvCommit.isVisible = true
            binding.tvRetake.isVisible = false
            savePpgResult()
        }else{
            binding.tvCollectionState.text = getString(R.string.str_data_collection_failed)
            binding.tvCollectionState.setTextColor("#FF0000".toColorInt())
            binding.tvCancel.isVisible = false
            binding.tvCommit.isVisible = false
            binding.tvRetake.isVisible = true
        }
    }

    /**
     * 终止评估
     */
    private fun terminateEvaluation(){
        isComplete = false
        isTerminate = true
        binding.tvCollectionState.isVisible = false
        binding.tvTerminate.isVisible = false
        binding.tvRetake.isVisible = true
        binding.tvCancel.isVisible = false
        binding.tvCommit.isVisible = false
        binding.ecgView.setDisplayMode(ECGView.DisplayMode.STATIC)
        stopTimeCountdown()
    }

    /**
     * 重新评估
     */
    private fun retakeEvaluation(){
        isComplete = false
        isTerminate = false
        binding.tvCollectionState.isVisible = false
        binding.tvTerminate.isVisible = true
        binding.tvRetake.isVisible = false
        binding.tvCancel.isVisible = false
        binding.tvCommit.isVisible = false
        binding.ecgView.clear()
        binding.ecgView.setDisplayMode(ECGView.DisplayMode.REALTIME)
        startTimeCountdown()
    }

    /**
     * 评估异常(由于设备断开连接等原因)
     */
    private fun evaluatingAnomaly(){
        isComplete = false
        isTerminate = true
        binding.tvCollectionState.isVisible = true
        binding.tvCollectionState.text = getString(R.string.str_data_collection_failed)
        binding.tvCollectionState.setTextColor("#FF0000".toColorInt())
        binding.tvTerminate.isVisible = false
        binding.tvRetake.isVisible = true
        binding.tvCancel.isVisible = false
        binding.tvCommit.isVisible = false
        binding.ecgView.setDisplayMode(ECGView.DisplayMode.STATIC)
        stopTimeCountdown()
    }

    /**
     * 保存PPG数据
     */
    private fun savePpgResult(){
        ppgAnalysisResult?.let {
            val result = gson.toJson(it)
            PPGManager.printAnalysisResult(it)
            ppgDataFile?.let { file ->
                launch(Dispatchers.IO) {
                    PPGManager.createFormattedJsonFile(gson.toJson(ppgDataPointList), file, "${simpleDateFormat}_ppg")
                    PPGManager.createFormattedJsonFile(result, file, simpleDateFormat)
                }
            }
        }
    }

    /**
     * 评估结果上报
     */
    private fun reportEvaluationData(){
        ppgAnalysisResult?.let {
            val result = gson.toJson(it)
            evaluationVM.reportEvaluationData(result)
        }
    }

}